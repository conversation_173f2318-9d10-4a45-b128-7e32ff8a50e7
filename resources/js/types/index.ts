export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string;
}

export interface Dress {
    id: string;
    name: string;
    description: string;
    images: string[];
    price: number;
    deposit: number;
    category: string;
    color: string;
    sizes: string[];
    available: boolean;
    rating: number;
    review_count: number;
    unavailable_dates: string[];
    created_at?: string;
    updated_at?: string;
}

export interface CartItem {
    dress: Dress;
    start_date: string;
    end_date: string;
    size: string;
    duration: number;
    total_price: number;
}

export interface BookingForm {
    full_name: string;
    email: string;
    phone: string;
    address: string;
    agree_to_terms: boolean;
}

export interface Order {
    id: string;
    dress: Dress;
    start_date: string;
    end_date: string;
    size: string;
    duration: number;
    total_price: number;
    deposit: number;
    status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled';
    customer_info: BookingForm;
    order_date: string;
    created_at?: string;
    updated_at?: string;
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
};
