import React from 'react';
import { Instagram, Facebook, Phone, Mail, MapPin } from 'lucide-react';
import { Link } from '@inertiajs/react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-[#493628] text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold mb-4">
              GaunSyari<span className="text-[#D6C0B3]">Jogja</span>
            </h3>
            <p className="text-[#D6C0B3] mb-4 leading-relaxed">
              Penyedia layanan sewa gaun syari terpercaya di Yogyakarta. 
              Kami menghadirkan koleksi gaun syari berkualitas untuk berbagai acara istimewa Anda.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-[#D6C0B3] hover:text-white transition-colors">
                <Instagram className="h-6 w-6" />
              </a>
              <a href="#" className="text-[#D6C0B3] hover:text-white transition-colors">
                <Facebook className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Tautan Cepat</h4>
            <ul className="space-y-2 text-[#D6C0B3]">
              <li><Link href="/catalog" className="hover:text-white transition-colors">Katalog Gaun</Link></li>
              <li><Link href="/how-to-order" className="hover:text-white transition-colors">Cara Pemesanan</Link></li>
              <li><Link href="/terms" className="hover:text-white transition-colors">Syarat & Ketentuan</Link></li>
              <li><Link href="/privacy" className="hover:text-white transition-colors">Kebijakan Privasi</Link></li>
              <li><Link href="/faq" className="hover:text-white transition-colors">FAQ</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Kontak Kami</h4>
            <div className="space-y-3 text-[#D6C0B3]">
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5" />
                <span>+62 812-3456-7890</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 mt-1" />
                <span>Jl. Malioboro No. 123, Yogyakarta 55271</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-[#AB886D] mt-8 pt-8 text-center text-[#D6C0B3]">
          <p>&copy; 2025 GaunSyariJogja.com. Hak Cipta Dilindungi.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
