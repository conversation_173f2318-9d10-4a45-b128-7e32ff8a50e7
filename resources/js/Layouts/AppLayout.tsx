import React, { ReactNode } from 'react';
import { Head } from '@inertiajs/react';

interface AppLayoutProps {
    children: ReactNode;
    title?: string;
}

export default function AppLayout({ children, title }: AppLayoutProps) {
    return (
        <>
            <Head title={title} />
            <div className="min-h-screen bg-gray-100">
                <main>{children}</main>
            </div>
        </>
    );
}
