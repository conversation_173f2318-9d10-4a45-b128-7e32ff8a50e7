{"name": "nunomaduro/collision", "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["console", "command-line", "php", "cli", "error", "handling", "laravel-zero", "laravel", "artisan", "symfony", "dev"], "license": "MIT", "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "filp/whoops": "^2.18.1", "nunomaduro/termwind": "^2.3.1", "symfony/console": "^7.3.0"}, "conflict": {"laravel/framework": "<11.44.2 || >=13.0.0", "phpunit/phpunit": "<11.5.15 || >=13.0.0"}, "require-dev": {"brianium/paratest": "^7.8.3", "laravel/framework": "^11.44.2 || ^12.18", "laravel/pint": "^1.22.1", "laravel/tinker": "^2.10.1", "laravel/sail": "^1.43.1", "laravel/sanctum": "^4.1.1", "larastan/larastan": "^3.4.2", "orchestra/testbench-core": "^9.12.0 || ^10.4", "pestphp/pest": "^3.8.2", "sebastian/environment": "^7.2.1 || ^8.0"}, "autoload-dev": {"psr-4": {"Tests\\Printer\\": "tests/Printer", "Tests\\Unit\\": "tests/Unit", "Tests\\FakeProgram\\": "tests/FakeProgram", "Tests\\": "tests/LaravelApp/tests", "App\\": "tests/LaravelApp/app/"}}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}, "files": ["./src/Adapters/Phpunit/Autoload.php"]}, "config": {"preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}, "branch-alias": {"dev-8.x": "8.x-dev"}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit:phpunit": ["@putenv XDEBUG_MODE=coverage", "phpunit --colors=always"], "test:unit:pest": ["@putenv XDEBUG_MODE=coverage", "pest --colors=always -v"], "test": ["@test:lint", "@test:types", "@test:unit:phpunit", "@test:unit:pest"]}}