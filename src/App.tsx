import React, { useState } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import HomePage from './components/HomePage';
import CatalogPage from './components/CatalogPage';
import DetailPage from './components/DetailPage';
import BookingPage from './components/BookingPage';
import ConfirmationPage from './components/ConfirmationPage';
import AccountPage from './components/AccountPage';

type Page = 'home' | 'catalog' | 'detail' | 'booking' | 'confirmation' | 'account' | 'about' | 'contact';

function App() {
  const [currentPage, setCurrentPage] = useState<Page>('home');
  const [selectedDressId, setSelectedDressId] = useState<string>('');
  const [currentOrderId, setCurrentOrderId] = useState<string>('');

  const handleNavigate = (page: string, id?: string) => {
    setCurrentPage(page as Page);
    if (id) {
      if (page === 'detail' || page === 'booking') {
        setSelectedDressId(id);
      } else if (page === 'confirmation') {
        setCurrentOrderId(id);
      }
    }
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage onNavigate={handleNavigate} />;
      case 'catalog':
        return <CatalogPage onNavigate={handleNavigate} />;
      case 'detail':
        return <DetailPage dressId={selectedDressId} onNavigate={handleNavigate} />;
      case 'booking':
        return <BookingPage dressId={selectedDressId} onNavigate={handleNavigate} />;
      case 'confirmation':
        return <ConfirmationPage orderId={currentOrderId} onNavigate={handleNavigate} />;
      case 'account':
        return <AccountPage onNavigate={handleNavigate} />;
      case 'about':
        return (
          <div className="min-h-screen bg-[#E4E0E1] flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-[#493628] mb-4">Tentang Kami</h1>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl">
                GaunSyariJogja adalah penyedia layanan sewa gaun syari terpercaya di Yogyakarta. 
                Kami berkomitmen memberikan koleksi gaun berkualitas untuk berbagai acara istimewa Anda.
              </p>
              <button 
                onClick={() => handleNavigate('home')}
                className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
              >
                Kembali ke Beranda
              </button>
            </div>
          </div>
        );
      case 'contact':
        return (
          <div className="min-h-screen bg-[#E4E0E1] flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-[#493628] mb-4">Hubungi Kami</h1>
              <div className="text-lg text-gray-600 mb-8 space-y-2">
                <p>📱 WhatsApp: +62 812-3456-7890</p>
                <p>📧 Email: <EMAIL></p>
                <p>📍 Alamat: Jl. Malioboro No. 123, Yogyakarta</p>
              </div>
              <button 
                onClick={() => handleNavigate('home')}
                className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
              >
                Kembali ke Beranda
              </button>
            </div>
          </div>
        );
      default:
        return <HomePage onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header currentPage={currentPage} onNavigate={handleNavigate} />
      <main className="flex-grow">
        {renderPage()}
      </main>
      <Footer />
    </div>
  );
}

export default App;