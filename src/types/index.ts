export interface Dress {
  id: string;
  name: string;
  description: string;
  images: string[];
  price: number;
  deposit: number;
  category: string;
  color: string;
  sizes: string[];
  available: boolean;
  rating: number;
  reviewCount: number;
  unavailableDates: string[];
}

export interface CartItem {
  dress: Dress;
  startDate: string;
  endDate: string;
  size: string;
  duration: number;
  totalPrice: number;
}

export interface BookingForm {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  agreeToTerms: boolean;
}

export interface Order {
  id: string;
  dress: Dress;
  startDate: string;
  endDate: string;
  size: string;
  duration: number;
  totalPrice: number;
  deposit: number;
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled';
  customerInfo: BookingForm;
  orderDate: string;
}