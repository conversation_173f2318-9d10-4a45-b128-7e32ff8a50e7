import React, { useState } from 'react';
import { Search, ShoppingBag, Menu, X, User } from 'lucide-react';

interface HeaderProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

const Header: React.FC<HeaderProps> = ({ currentPage, onNavigate }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navItems = [
    { id: 'home', label: 'Beranda' },
    { id: 'catalog', label: 'Katalog' },
    { id: 'about', label: 'Tentang Kami' },
    { id: 'contact', label: 'Kontak' }
  ];

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0 cursor-pointer" onClick={() => onNavigate('home')}>
            <h1 className="text-2xl font-bold text-[#493628]">
              GaunSyari<span className="text-[#AB886D]">Jogja</span>
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => onNavigate(item.id)}
                className={`px-3 py-2 text-sm font-medium transition-colors duration-200 ${
                  currentPage === item.id
                    ? 'text-[#493628] border-b-2 border-[#AB886D]'
                    : 'text-gray-600 hover:text-[#493628]'
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Right Section */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="p-2 text-gray-600 hover:text-[#493628] transition-colors">
              <Search className="h-5 w-5" />
            </button>
            <button 
              onClick={() => onNavigate('account')}
              className="p-2 text-gray-600 hover:text-[#493628] transition-colors"
            >
              <User className="h-5 w-5" />
            </button>
            <button className="p-2 text-gray-600 hover:text-[#493628] transition-colors">
              <ShoppingBag className="h-5 w-5" />
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-600 hover:text-[#493628]"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onNavigate(item.id);
                    setIsMenuOpen(false);
                  }}
                  className={`block w-full text-left px-3 py-2 text-base font-medium transition-colors ${
                    currentPage === item.id
                      ? 'text-[#493628] bg-[#E4E0E1]'
                      : 'text-gray-600 hover:text-[#493628] hover:bg-[#E4E0E1]'
                  }`}
                >
                  {item.label}
                </button>
              ))}
              <div className="flex items-center justify-center space-x-4 pt-4 border-t">
                <button className="p-2 text-gray-600 hover:text-[#493628]">
                  <Search className="h-5 w-5" />
                </button>
                <button 
                  onClick={() => {
                    onNavigate('account');
                    setIsMenuOpen(false);
                  }}
                  className="p-2 text-gray-600 hover:text-[#493628]"
                >
                  <User className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-600 hover:text-[#493628]">
                  <ShoppingBag className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;