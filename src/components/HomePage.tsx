import React from 'react';
import { <PERSON>, <PERSON>R<PERSON>, Clock, Shield, Award } from 'lucide-react';
import { dresses } from '../data/dresses';
import { Dress } from '../types';

interface HomePageProps {
  onNavigate: (page: string, dressId?: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  const featuredDresses = dresses.slice(0, 3);


  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      {/* Hero Section */}
      <section className="relative bg-[#493628] text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-[#493628] via-[#AB886D] to-[#D6C0B3] opacity-90"></div>
        <div className="absolute top-0 right-0 w-1/2 h-full">
          <img 
            src="https://images.pexels.com/photos/9594137/pexels-photo-9594137.jpeg?auto=compress&cs=tinysrgb&w=1200" 
            alt="Elegant Syari Dress"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
              Gaun Syari Elegan
              <span className="block text-[#D6C0B3] text-4xl md:text-6xl mt-2">untuk Momen Istimewa</span>
            </h1>
            <p className="text-xl md:text-2xl mb-10 text-white/90 max-w-4xl mx-auto leading-relaxed">
              Sewa gaun syari berkualitas premium untuk pernikahan, pesta, dan acara formal. 
              Koleksi terlengkap di Yogyakarta dengan pelayanan terpercaya.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button 
                onClick={() => onNavigate('catalog')}
                className="bg-white text-[#493628] px-10 py-4 rounded-full font-bold text-lg hover:bg-[#D6C0B3] hover:text-white transition-all duration-300 flex items-center justify-center shadow-lg transform hover:scale-105"
              >
                Jelajahi Koleksi
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
              <button 
                onClick={() => onNavigate('contact')}
                className="border-3 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-[#493628] transition-all duration-300 shadow-lg"
              >
                Konsultasi Gratis
              </button>
            </div>
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-[#D6C0B3] mb-2">500+</div>
                <div className="text-white/80">Gaun Tersedia</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#D6C0B3] mb-2">1000+</div>
                <div className="text-white/80">Pelanggan Puas</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#D6C0B3] mb-2">5+</div>
                <div className="text-white/80">Tahun Pengalaman</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-xl font-semibold text-[#493628] mb-2">Proses Cepat</h3>
              <p className="text-gray-600">Pemesanan mudah dan proses konfirmasi dalam 24 jam</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-xl font-semibold text-[#493628] mb-2">Terpercaya</h3>
              <p className="text-gray-600">Gaun berkualitas tinggi dengan jaminan kebersihan</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-xl font-semibold text-[#493628] mb-2">Kualitas Terbaik</h3>
              <p className="text-gray-600">Koleksi gaun pilihan dengan desain terkini</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Dresses */}
      <section className="py-16 bg-[#E4E0E1]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#493628] mb-4">
              Gaun Pilihan Terbaru
            </h2>
            <p className="text-xl text-gray-600">
              Koleksi terbaik kami yang paling diminati
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredDresses.map((dress) => (
              <div key={dress.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative">
                  <img 
                    src={dress.images[0]} 
                    alt={dress.name}
                    className="w-full h-64 object-cover"
                  />
                  {!dress.available && (
                    <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm">
                      Tidak Tersedia
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-[#493628] mb-2">{dress.name}</h3>
                  <p className="text-gray-600 mb-4 text-sm line-clamp-2">{dress.description}</p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-lg font-bold text-[#493628]">
                        Rp {dress.price.toLocaleString()}/hari
                      </p>
                      <p className="text-sm text-gray-500">DP: Rp {dress.deposit.toLocaleString()}</p>
                    </div>
                    <button 
                      onClick={() => onNavigate('detail', dress.id)}
                      className="bg-[#AB886D] text-white px-4 py-2 rounded-lg hover:bg-[#493628] transition-colors duration-300"
                    >
                      Lihat Detail
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <button 
              onClick={() => onNavigate('catalog')}
              className="bg-[#493628] text-white px-8 py-3 rounded-lg font-semibold hover:bg-[#AB886D] transition-colors duration-300"
            >
              Lihat Semua Koleksi
            </button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#493628] mb-4">
              Mengapa Memilih Kami?
            </h2>
            <p className="text-xl text-gray-600">
              Komitmen kami untuk memberikan pelayanan terbaik
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 bg-[#E4E0E1] rounded-xl">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-lg font-semibold text-[#493628] mb-2">Booking Mudah</h3>
              <p className="text-gray-600 text-sm">Proses pemesanan online yang cepat dan praktis</p>
            </div>
            <div className="text-center p-6 bg-[#E4E0E1] rounded-xl">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-lg font-semibold text-[#493628] mb-2">Gaun Bersih</h3>
              <p className="text-gray-600 text-sm">Jaminan kebersihan dan kualitas setiap gaun</p>
            </div>
            <div className="text-center p-6 bg-[#E4E0E1] rounded-xl">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-lg font-semibold text-[#493628] mb-2">Koleksi Lengkap</h3>
              <p className="text-gray-600 text-sm">Berbagai pilihan gaun untuk setiap acara</p>
            </div>
            <div className="text-center p-6 bg-[#E4E0E1] rounded-xl">
              <div className="w-16 h-16 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-[#493628]" />
              </div>
              <h3 className="text-lg font-semibold text-[#493628] mb-2">Harga Terjangkau</h3>
              <p className="text-gray-600 text-sm">Tarif sewa yang kompetitif dan transparan</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;