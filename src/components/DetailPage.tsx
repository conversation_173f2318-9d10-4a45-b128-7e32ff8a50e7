import React, { useState } from 'react';
import { ArrowLeft, Calendar, ShoppingBag, Heart, Share2, ZoomIn, ChevronLeft, ChevronRight } from 'lucide-react';
import { dresses } from '../data/dresses';
import { Dress } from '../types';

interface DetailPageProps {
  dressId: string;
  onNavigate: (page: string, dressId?: string) => void;
}

const DetailPage: React.FC<DetailPageProps> = ({ dressId, onNavigate }) => {
  const dress = dresses.find(d => d.id === dressId);
  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedSize, setSelectedSize] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [duration, setDuration] = useState(0);
  const [totalPrice, setTotalPrice] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDates, setSelectedDates] = useState<{ start: Date | null; end: Date | null }>({
    start: null,
    end: null
  });

  // Mock booked dates for demonstration
  const bookedDates = [
    '2025-01-15', '2025-01-16', '2025-01-17',
    '2025-01-25', '2025-01-26',
    '2025-02-14', '2025-02-15'
  ];

  if (!dress) {
    return (
      <div className="min-h-screen bg-[#E4E0E1] flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-gray-600 mb-4">Gaun tidak ditemukan</p>
          <button 
            onClick={() => onNavigate('catalog')}
            className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
          >
            Kembali ke Katalog
          </button>
        </div>
      </div>
    );
  }

  const calculateDuration = (start: string, end: string) => {
    if (start && end) {
      const startDateTime = new Date(start);
      const endDateTime = new Date(end);
      const diffTime = endDateTime.getTime() - startDateTime.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays > 0 ? diffDays : 0;
    }
    return 0;
  };

  const handleDateChange = (start: string, end: string) => {
    const days = calculateDuration(start, end);
    setDuration(days);
    setTotalPrice(days * dress.price);
  };

  const handleDateSelect = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    
    // Check if date is booked
    if (bookedDates.includes(dateStr)) return;
    
    if (!selectedDates.start || (selectedDates.start && selectedDates.end)) {
      // Start new selection
      setSelectedDates({ start: date, end: null });
      setStartDate(dateStr);
      setEndDate('');
    } else if (selectedDates.start && !selectedDates.end) {
      // Complete selection
      if (date < selectedDates.start) {
        setSelectedDates({ start: date, end: selectedDates.start });
        setStartDate(dateStr);
        setEndDate(selectedDates.start.toISOString().split('T')[0]);
      } else {
        setSelectedDates({ start: selectedDates.start, end: date });
        setEndDate(dateStr);
      }
    }
  };

  const isDateInRange = (date: Date) => {
    if (!selectedDates.start || !selectedDates.end) return false;
    return date >= selectedDates.start && date <= selectedDates.end;
  };

  const isDateBooked = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return bookedDates.includes(dateStr);
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1);
      } else {
        newMonth.setMonth(prev.getMonth() + 1);
      }
      return newMonth;
    });
  };

  // Update duration and price when dates change
  React.useEffect(() => {
    if (selectedDates.start && selectedDates.end) {
      const days = calculateDuration(
        selectedDates.start.toISOString().split('T')[0],
        selectedDates.end.toISOString().split('T')[0]
      );
      setDuration(days);
      setTotalPrice(days * dress.price);
    }
  }, [selectedDates, dress.price]);

  const handleBooking = () => {
    if (!selectedSize || !selectedDates.start || !selectedDates.end) {
      alert('Silakan pilih ukuran dan tanggal sewa terlebih dahulu');
      return;
    }
    
    // Navigate to booking page with dress data
    onNavigate('booking', dressId);
  };

  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button 
          onClick={() => onNavigate('catalog')}
          className="flex items-center text-[#493628] hover:text-[#AB886D] transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Kembali ke Katalog
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Image Gallery */}
          <div>
            <div className="relative mb-4">
              <img 
                src={dress.images[selectedImage]} 
                alt={dress.name}
                className="w-full h-96 lg:h-[500px] object-cover rounded-xl shadow-lg cursor-zoom-in"
                onClick={() => setShowImageModal(true)}
              />
              <button 
                onClick={() => setShowImageModal(true)}
                className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white transition-colors"
              >
                <ZoomIn className="h-5 w-5 text-[#493628]" />
              </button>
            </div>
            
            {dress.images.length > 1 && (
              <div className="flex gap-3 overflow-x-auto">
                {dress.images.map((image, index) => (
                  <img 
                    key={index}
                    src={image} 
                    alt={`${dress.name} ${index + 1}`}
                    className={`w-20 h-20 object-cover rounded-lg cursor-pointer flex-shrink-0 ${
                      selectedImage === index ? 'ring-2 ring-[#AB886D]' : ''
                    }`}
                    onClick={() => setSelectedImage(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div>
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-[#493628] mb-2">
                    {dress.name}
                  </h1>
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`h-4 w-4 ${i < Math.floor(dress.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                        />
                      ))}
                      <span className="text-sm text-gray-600 ml-2">
                        {dress.rating} ({dress.reviewCount} ulasan)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                    <Heart className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-[#493628] transition-colors">
                    <Share2 className="h-5 w-5" />
                  </button>
                  <div className="text-right">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      dress.available 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {dress.available ? 'Tersedia' : 'Tidak Tersedia'}
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Kategori</p>
                    <span className="bg-[#E4E0E1] text-[#493628] px-3 py-1 rounded-full text-sm">
                      {dress.category}
                    </span>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Warna</p>
                    <span className="bg-[#E4E0E1] text-[#493628] px-3 py-1 rounded-full text-sm">
                      {dress.color}
                    </span>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 mb-6">
                <h3 className="text-lg font-semibold text-[#493628] mb-2">Deskripsi</h3>
                <p className="text-gray-600 leading-relaxed">{dress.description}</p>
              </div>

              {dress.available && (
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-semibold text-[#493628] mb-4">Pilih Ukuran & Durasi Sewa</h3>
                  
                  {/* Size Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Ukuran</label>
                    <div className="flex gap-2">
                      {dress.sizes.map(size => (
                        <button
                          key={size}
                          onClick={() => setSelectedSize(size)}
                          className={`px-4 py-2 border rounded-lg transition-colors ${
                            selectedSize === size
                              ? 'border-[#AB886D] bg-[#AB886D] text-white'
                              : 'border-gray-300 text-gray-700 hover:border-[#AB886D]'
                          }`}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Calendar Selection */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Pilih Tanggal Sewa (Klik tanggal mulai dan selesai)
                    </label>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      {/* Calendar Header */}
                      <div className="flex items-center justify-between mb-4">
                        <button
                          onClick={() => navigateMonth('prev')}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        >
                          <ChevronLeft className="h-5 w-5 text-gray-600" />
                        </button>
                        <h4 className="text-lg font-semibold text-[#493628]">
                          {currentMonth.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' })}
                        </h4>
                        <button
                          onClick={() => navigateMonth('next')}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        >
                          <ChevronRight className="h-5 w-5 text-gray-600" />
                        </button>
                      </div>
                      
                      {/* Calendar Grid */}
                      <div className="grid grid-cols-7 gap-1 mb-2">
                        {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                          <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                            {day}
                          </div>
                        ))}
                      </div>
                      
                      <div className="grid grid-cols-7 gap-1">
                        {getDaysInMonth(currentMonth).map((date, index) => {
                          if (!date) {
                            return <div key={index} className="p-2"></div>;
                          }
                          
                          const isToday = date.toDateString() === new Date().toDateString();
                          const isPast = date < new Date(new Date().setHours(0, 0, 0, 0));
                          const isBooked = isDateBooked(date);
                          const isSelected = selectedDates.start?.toDateString() === date.toDateString() || 
                                           selectedDates.end?.toDateString() === date.toDateString();
                          const isInRange = isDateInRange(date);
                          
                          let buttonClass = 'w-full p-2 text-sm rounded-lg transition-colors ';
                          
                          if (isPast) {
                            buttonClass += 'text-gray-300 cursor-not-allowed';
                          } else if (isBooked) {
                            buttonClass += 'bg-red-100 text-red-600 cursor-not-allowed';
                          } else if (isSelected) {
                            buttonClass += 'bg-[#493628] text-white';
                          } else if (isInRange) {
                            buttonClass += 'bg-[#D6C0B3] text-[#493628]';
                          } else if (isToday) {
                            buttonClass += 'bg-[#AB886D] text-white hover:bg-[#493628]';
                          } else {
                            buttonClass += 'hover:bg-[#E4E0E1] text-gray-700';
                          }
                          
                          return (
                            <button
                              key={index}
                              onClick={() => !isPast && !isBooked && handleDateSelect(date)}
                              disabled={isPast || isBooked}
                              className={buttonClass}
                            >
                              {date.getDate()}
                            </button>
                          );
                        })}
                      </div>
                      
                      {/* Legend */}
                      <div className="flex flex-wrap gap-4 mt-4 text-xs">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-[#493628] rounded mr-2"></div>
                          <span>Tanggal Dipilih</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-[#D6C0B3] rounded mr-2"></div>
                          <span>Rentang Sewa</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-red-100 border border-red-300 rounded mr-2"></div>
                          <span>Tidak Tersedia</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-[#AB886D] rounded mr-2"></div>
                          <span>Hari Ini</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Duration and Price Calculation */}
                  {duration > 0 && (
                    <div className="bg-[#E4E0E1] p-4 rounded-lg mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-700">Tanggal Sewa:</span>
                        <span className="font-semibold text-[#493628]">
                          {selectedDates.start?.toLocaleDateString('id-ID')} - {selectedDates.end?.toLocaleDateString('id-ID')}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-700">Durasi Sewa:</span>
                        <span className="font-semibold text-[#493628]">{duration} hari</span>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-gray-700">Total Sewa:</span>
                        <span className="font-bold text-[#493628] text-lg">
                          Rp {totalPrice.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-gray-700">DP yang dibayar:</span>
                        <span className="font-semibold text-[#AB886D]">
                          Rp {dress.deposit.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Booking Button */}
                  <button 
                    onClick={handleBooking}
                    disabled={!selectedSize || !selectedDates.start || !selectedDates.end}
                    className="w-full bg-[#493628] text-white py-3 px-6 rounded-lg font-semibold hover:bg-[#AB886D] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <ShoppingBag className="h-5 w-5 mr-2" />
                    Pesan Sekarang
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img 
              src={dress.images[selectedImage]} 
              alt={dress.name}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <button 
              onClick={() => setShowImageModal(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailPage;