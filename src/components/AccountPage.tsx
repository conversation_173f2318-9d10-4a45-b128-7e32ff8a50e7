import React, { useState } from 'react';
import { User, Package, Clock, CheckCircle2, XCircle, Calendar, ArrowLeft } from 'lucide-react';
import { dresses } from '../data/dresses';
import { Order } from '../types';

interface AccountPageProps {
  onNavigate: (page: string, dressId?: string) => void;
}

const AccountPage: React.FC<AccountPageProps> = ({ onNavigate }) => {
  const [activeTab, setActiveTab] = useState<'profile' | 'orders'>('orders');

  // Mock order data - in real app, this would be fetched from API
  const mockOrders: Order[] = [
    {
      id: 'ORD-**********',
      dress: dresses[0],
      startDate: '2025-01-20',
      endDate: '2025-01-22',
      size: 'M',
      duration: 3,
      totalPrice: dresses[0].price * 3,
      deposit: dresses[0].deposit,
      status: 'pending',
      customerInfo: {
        fullName: 'Siti Aminah',
        email: '<EMAIL>',
        phone: '***********',
        address: 'Jl. Malioboro No. 45, Yogyakarta',
        agreeToTerms: true
      },
      orderDate: '2025-01-15'
    },
    {
      id: 'ORD-**********',
      dress: dresses[1],
      startDate: '2025-01-10',
      endDate: '2025-01-12',
      size: 'L',
      duration: 3,
      totalPrice: dresses[1].price * 3,
      deposit: dresses[1].deposit,
      status: 'completed',
      customerInfo: {
        fullName: 'Siti Aminah',
        email: '<EMAIL>',
        phone: '***********',
        address: 'Jl. Malioboro No. 45, Yogyakarta',
        agreeToTerms: true
      },
      orderDate: '2025-01-05'
    },
    {
      id: 'ORD-1641234565',
      dress: dresses[2],
      startDate: '2024-12-25',
      endDate: '2024-12-27',
      size: 'S',
      duration: 3,
      totalPrice: dresses[2].price * 3,
      deposit: dresses[2].deposit,
      status: 'cancelled',
      customerInfo: {
        fullName: 'Siti Aminah',
        email: '<EMAIL>',
        phone: '***********',
        address: 'Jl. Malioboro No. 45, Yogyakarta',
        agreeToTerms: true
      },
      orderDate: '2024-12-20'
    }
  ];

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircle2 className="h-5 w-5 text-blue-500" />;
      case 'active':
        return <Package className="h-5 w-5 text-green-500" />;
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'Menunggu Pembayaran';
      case 'confirmed':
        return 'Dikonfirmasi';
      case 'active':
        return 'Sedang Disewa';
      case 'completed':
        return 'Selesai';
      case 'cancelled':
        return 'Dibatalkan';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button 
          onClick={() => onNavigate('home')}
          className="flex items-center text-[#493628] hover:text-[#AB886D] transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Kembali ke Beranda
        </button>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-10 w-10 text-[#493628]" />
          </div>
          <h1 className="text-3xl font-bold text-[#493628] mb-2">Akun Saya</h1>
          <p className="text-lg text-gray-600">Kelola informasi dan riwayat pemesanan Anda</p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-t-xl shadow-lg">
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('orders')}
              className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
                activeTab === 'orders'
                  ? 'text-[#493628] border-b-2 border-[#AB886D] bg-[#E4E0E1]'
                  : 'text-gray-600 hover:text-[#493628]'
              }`}
            >
              <Package className="h-5 w-5 inline-block mr-2" />
              Riwayat Pemesanan
            </button>
            <button
              onClick={() => setActiveTab('profile')}
              className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
                activeTab === 'profile'
                  ? 'text-[#493628] border-b-2 border-[#AB886D] bg-[#E4E0E1]'
                  : 'text-gray-600 hover:text-[#493628]'
              }`}
            >
              <User className="h-5 w-5 inline-block mr-2" />
              Profil Saya
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-b-xl shadow-lg p-6">
          {activeTab === 'orders' ? (
            <div>
              <h2 className="text-2xl font-bold text-[#493628] mb-6">Riwayat Pemesanan</h2>
              
              {mockOrders.length === 0 ? (
                <div className="text-center py-12">
                  <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-xl text-gray-600 mb-4">Belum ada pemesanan</p>
                  <button 
                    onClick={() => onNavigate('catalog')}
                    className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
                  >
                    Mulai Berbelanja
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  {mockOrders.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                        {/* Order Image */}
                        <div className="flex-shrink-0">
                          <img 
                            src={order.dress.images[0]} 
                            alt={order.dress.name}
                            className="w-24 h-24 object-cover rounded-lg"
                          />
                        </div>

                        {/* Order Details */}
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
                            <div>
                              <h3 className="text-lg font-semibold text-[#493628] mb-1">
                                {order.dress.name}
                              </h3>
                              <p className="text-sm text-gray-600 mb-2">
                                Order ID: {order.id}
                              </p>
                              <div className="flex items-center mb-2">
                                {getStatusIcon(order.status)}
                                <span className={`ml-2 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                  {getStatusText(order.status)}
                                </span>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-lg font-bold text-[#493628]">
                                Rp {(order.totalPrice + order.deposit).toLocaleString()}
                              </p>
                              <p className="text-sm text-gray-500">Total Pembayaran</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">Tanggal Pemesanan</p>
                              <p className="font-medium text-[#493628]">
                                {new Date(order.orderDate).toLocaleDateString('id-ID')}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Tanggal Sewa</p>
                              <p className="font-medium text-[#493628]">
                                {new Date(order.startDate).toLocaleDateString('id-ID')} - {new Date(order.endDate).toLocaleDateString('id-ID')}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Ukuran & Durasi</p>
                              <p className="font-medium text-[#493628]">
                                {order.size} - {order.duration} hari
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col gap-2 lg:flex-shrink-0">
                          <button 
                            onClick={() => onNavigate('detail', order.dress.id)}
                            className="px-4 py-2 border border-[#AB886D] text-[#AB886D] rounded-lg hover:bg-[#AB886D] hover:text-white transition-colors text-sm"
                          >
                            Lihat Detail
                          </button>
                          {order.status === 'completed' && (
                            <button className="px-4 py-2 bg-[#493628] text-white rounded-lg hover:bg-[#AB886D] transition-colors text-sm">
                              Pesan Lagi
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div>
              <h2 className="text-2xl font-bold text-[#493628] mb-6">Profil Saya</h2>
              
              <div className="max-w-2xl">
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nama Lengkap
                      </label>
                      <input
                        type="text"
                        defaultValue="Siti Aminah"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nomor Telepon
                      </label>
                      <input
                        type="tel"
                        defaultValue="***********"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tanggal Lahir
                      </label>
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Alamat Lengkap
                    </label>
                    <textarea
                      rows={3}
                      defaultValue="Jl. Malioboro No. 45, Yogyakarta"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent"
                    />
                  </div>

                  <div className="flex gap-4">
                    <button 
                      type="submit"
                      className="bg-[#493628] text-white px-6 py-3 rounded-lg hover:bg-[#AB886D] transition-colors"
                    >
                      Simpan Perubahan
                    </button>
                    <button 
                      type="button"
                      className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Batal
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccountPage;