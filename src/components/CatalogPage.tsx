import React, { useState, useMemo } from 'react';
import { Search, Filter, Star } from 'lucide-react';
import { dresses } from '../data/dresses';
import { Dress } from '../types';

interface CatalogPageProps {
  onNavigate: (page: string, dressId?: string) => void;
}

const CatalogPage: React.FC<CatalogPageProps> = ({ onNavigate }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [priceRange, setPriceRange] = useState({ min: 0, max: 300000 });
  const [showFilters, setShowFilters] = useState(false);

  const categories = ['Gaun Pesta', 'Gaun Akad', 'Gaun Formal', 'Gaun Malam'];
  const colors = ['Maroon', 'Navy Blue', 'Pink', 'Black', 'Green', 'Cream'];

  const filteredDresses = useMemo(() => {
    return dresses.filter(dress => {
      const matchesSearch = dress.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           dress.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === '' || dress.category === selectedCategory;
      const matchesColor = selectedColor === '' || dress.color === selectedColor;
      const matchesPrice = dress.price >= priceRange.min && dress.price <= priceRange.max;
      
      return matchesSearch && matchesCategory && matchesColor && matchesPrice;
    });
  }, [searchTerm, selectedCategory, selectedColor, priceRange]);

  const resetFilters = () => {
    setSelectedCategory('');
    setSelectedColor('');
    setPriceRange({ min: 0, max: 300000 });
    setSearchTerm('');
  };

  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-[#493628] mb-4">
            Katalog Gaun Syari
          </h1>
          <p className="text-lg text-gray-600">
            Temukan gaun syari impian Anda dari koleksi terlengkap kami
          </p>
        </div>

        {/* Search and Filter Controls */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          {/* Search */}
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Cari gaun berdasarkan nama atau deskripsi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent text-lg"
              />
            </div>
            
            {/* Filter Button for Mobile */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden flex items-center justify-center gap-2 px-6 py-3 bg-[#AB886D] text-white rounded-lg hover:bg-[#493628] transition-colors font-medium"
            >
              <Filter className="h-5 w-5" />
              {showFilters ? 'Tutup Filter' : 'Tampilkan Filter'}
            </button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            Menampilkan {filteredDresses.length} dari {dresses.length} gaun
          </p>
        </div>

        {/* Main Content with Sidebar */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden'} lg:block`}>
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-[#493628]">Filter Gaun</h3>
                <button
                  onClick={resetFilters}
                  className="text-sm text-[#AB886D] hover:text-[#493628] transition-colors"
                >
                  Reset Semua
                </button>
              </div>
              
              <div className="space-y-6">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Kategori</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        value=""
                        checked={selectedCategory === ''}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="mr-3 text-[#AB886D] focus:ring-[#AB886D]"
                      />
                      <span className="text-gray-700">Semua Kategori</span>
                    </label>
                    {categories.map(category => (
                      <label key={category} className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          value={category}
                          checked={selectedCategory === category}
                          onChange={(e) => setSelectedCategory(e.target.value)}
                          className="mr-3 text-[#AB886D] focus:ring-[#AB886D]"
                        />
                        <span className="text-gray-700">{category}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Color Filter */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Warna</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="color"
                        value=""
                        checked={selectedColor === ''}
                        onChange={(e) => setSelectedColor(e.target.value)}
                        className="mr-3 text-[#AB886D] focus:ring-[#AB886D]"
                      />
                      <span className="text-gray-700">Semua Warna</span>
                    </label>
                    {colors.map(color => (
                      <label key={color} className="flex items-center">
                        <input
                          type="radio"
                          name="color"
                          value={color}
                          checked={selectedColor === color}
                          onChange={(e) => setSelectedColor(e.target.value)}
                          className="mr-3 text-[#AB886D] focus:ring-[#AB886D]"
                        />
                        <span className="text-gray-700">{color}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Rentang Harga
                  </label>
                  <div className="space-y-3">
                    <div className="text-center p-3 bg-[#E4E0E1] rounded-lg">
                      <span className="text-lg font-bold text-[#493628]">
                        Rp {priceRange.min.toLocaleString()} - Rp {priceRange.max.toLocaleString()}
                      </span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="300000"
                      step="25000"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange({ ...priceRange, max: parseInt(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Rp 0</span>
                      <span>Rp 300.000</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Dress Grid */}
          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredDresses.map((dress) => (
                <div key={dress.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative">
                    <img 
                      src={dress.images[0]} 
                      alt={dress.name}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {!dress.available && (
                      <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm">
                        Tidak Tersedia
                      </div>
                    )}
                    <div className="absolute top-4 right-4 bg-[#493628] text-white px-3 py-1 rounded-full text-sm">
                      {dress.category}
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-[#493628] mb-2 line-clamp-1">
                      {dress.name}
                    </h3>
                    
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-gray-500 bg-[#E4E0E1] px-3 py-1 rounded-full">
                        {dress.color}
                      </span>
                      <span className={`text-sm px-3 py-1 rounded-full ${
                        dress.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {dress.available ? 'Tersedia' : 'Tidak Tersedia'}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {dress.description}
                    </p>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <p className="text-xl font-bold text-[#493628]">
                          Rp {dress.price.toLocaleString()}
                        </p>
                        <p className="text-sm text-gray-500">per hari</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">DP</p>
                        <p className="text-lg font-semibold text-[#AB886D]">
                          Rp {dress.deposit.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    
                    <button 
                      onClick={() => onNavigate('detail', dress.id)}
                      className="w-full bg-[#AB886D] text-white py-3 px-6 rounded-lg hover:bg-[#493628] transition-colors duration-300 font-semibold"
                    >
                      Lihat Detail & Sewa
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* No Results */}
        {filteredDresses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-xl text-gray-600 mb-4">
              Tidak ada gaun yang sesuai dengan kriteria pencarian Anda
            </p>
            <button
              onClick={resetFilters}
              className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
            >
              Reset Filter
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CatalogPage;