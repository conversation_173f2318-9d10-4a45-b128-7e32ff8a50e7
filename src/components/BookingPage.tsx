import React, { useState } from 'react';
import { ArrowLeft, CreditCard, AlertCircle, CheckCircle2 } from 'lucide-react';
import { dresses } from '../data/dresses';
import { BookingForm } from '../types';

interface BookingPageProps {
  dressId: string;
  onNavigate: (page: string, orderId?: string) => void;
}

const BookingPage: React.FC<BookingPageProps> = ({ dressId, onNavigate }) => {
  const dress = dresses.find(d => d.id === dressId);
  const [formData, setFormData] = useState<BookingForm>({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    agreeToTerms: false
  });
  const [errors, setErrors] = useState<Partial<BookingForm>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock data for demonstration - in real app, this would come from previous step
  const bookingDetails = {
    startDate: '2025-01-20',
    endDate: '2025-01-22',
    size: 'M',
    duration: 3,
    totalPrice: dress ? dress.price * 3 : 0
  };

  if (!dress) {
    return (
      <div className="min-h-screen bg-[#E4E0E1] flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-gray-600 mb-4">Gaun tidak ditemukan</p>
          <button 
            onClick={() => onNavigate('catalog')}
            className="bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
          >
            Kembali ke Katalog
          </button>
        </div>
      </div>
    );
  }

  const validateForm = () => {
    const newErrors: Partial<BookingForm> = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Nama lengkap wajib diisi';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email wajib diisi';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Nomor telepon wajib diisi';
    } else if (!/^[\d\-\+\(\)\s]+$/.test(formData.phone)) {
      newErrors.phone = 'Format nomor telepon tidak valid';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Alamat wajib diisi';
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'Anda harus menyetujui syarat dan ketentuan';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      const orderId = `ORD-${Date.now()}`;
      setIsSubmitting(false);
      onNavigate('confirmation', orderId);
    }, 2000);
  };

  const handleInputChange = (field: keyof BookingForm, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const totalPayment = bookingDetails.totalPrice + dress.deposit;

  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button 
          onClick={() => onNavigate('detail', dressId)}
          className="flex items-center text-[#493628] hover:text-[#AB886D] transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Kembali ke Detail Gaun
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
              <h2 className="text-xl font-bold text-[#493628] mb-4">Ringkasan Pesanan</h2>
              
              <div className="mb-4">
                <img 
                  src={dress.images[0]} 
                  alt={dress.name}
                  className="w-full h-32 object-cover rounded-lg mb-3"
                />
                <h3 className="font-semibold text-[#493628]">{dress.name}</h3>
                <p className="text-sm text-gray-600">{dress.category} - {dress.color}</p>
                <p className="text-sm text-gray-600">Ukuran: {bookingDetails.size}</p>
              </div>

              <div className="border-t border-gray-200 pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Tanggal Sewa:</span>
                  <span>{new Date(bookingDetails.startDate).toLocaleDateString('id-ID')} - {new Date(bookingDetails.endDate).toLocaleDateString('id-ID')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Durasi:</span>
                  <span>{bookingDetails.duration} hari</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Harga Sewa:</span>
                  <span>Rp {bookingDetails.totalPrice.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>DP (Uang Muka):</span>
                  <span>Rp {dress.deposit.toLocaleString()}</span>
                </div>
                <div className="border-t border-gray-200 pt-2 flex justify-between font-semibold">
                  <span>Total Pembayaran:</span>
                  <span className="text-[#493628]">Rp {totalPayment.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-2xl font-bold text-[#493628] mb-6">Detail Pemesanan</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Personal Information */}
                <div>
                  <h3 className="text-lg font-semibold text-[#493628] mb-4">Informasi Penyewa</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nama Lengkap *
                      </label>
                      <input
                        type="text"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange('fullName', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent ${
                          errors.fullName ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Masukkan nama lengkap"
                      />
                      {errors.fullName && (
                        <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent ${
                          errors.email ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nomor Telepon *
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent ${
                          errors.phone ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="08123456789"
                      />
                      {errors.phone && (
                        <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Alamat Lengkap *
                      </label>
                      <textarea
                        value={formData.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#AB886D] focus:border-transparent ${
                          errors.address ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Alamat lengkap untuk pengiriman/pengambilan"
                      />
                      {errors.address && (
                        <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Payment Method */}
                <div>
                  <h3 className="text-lg font-semibold text-[#493628] mb-4">Metode Pembayaran</h3>
                  <div className="bg-[#E4E0E1] p-4 rounded-lg">
                    <div className="flex items-center mb-3">
                      <CreditCard className="h-5 w-5 text-[#493628] mr-2" />
                      <span className="font-medium text-[#493628]">Transfer Bank</span>
                    </div>
                    <div className="space-y-2 text-sm text-gray-700">
                      <p><strong>Bank:</strong> Bank Mandiri</p>
                      <p><strong>No. Rekening:</strong> **********</p>
                      <p><strong>Atas Nama:</strong> GaunSyariJogja</p>
                      <p><strong>Jumlah Transfer:</strong> Rp {totalPayment.toLocaleString()}</p>
                    </div>
                    <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle className="h-4 w-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-yellow-800">
                          Harap selesaikan pembayaran dalam 1x24 jam setelah konfirmasi booking
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div>
                  <h3 className="text-lg font-semibold text-[#493628] mb-4">Syarat & Ketentuan</h3>
                  <div className="bg-gray-50 p-4 rounded-lg max-h-32 overflow-y-auto text-sm text-gray-700 mb-4">
                    <p className="mb-2">
                      1. Pembayaran DP wajib dilakukan maksimal 1x24 jam setelah konfirmasi booking.
                    </p>
                    <p className="mb-2">
                      2. Gaun harus dikembalikan dalam kondisi bersih dan tidak rusak.
                    </p>
                    <p className="mb-2">
                      3. Keterlambatan pengembalian dikenakan denda Rp 50.000/hari.
                    </p>
                    <p className="mb-2">
                      4. Kerusakan atau kehilangan gaun dikenakan biaya penggantian sesuai harga gaun.
                    </p>
                    <p>
                      5. Pembatalan booking hanya dapat dilakukan maksimal 3 hari sebelum tanggal acara.
                    </p>
                  </div>
                  
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                      className="mt-1 mr-3"
                    />
                    <label htmlFor="agreeToTerms" className="text-sm text-gray-700">
                      Saya telah membaca dan menyetujui <span className="text-[#493628] font-medium">Syarat & Ketentuan</span> yang berlaku
                    </label>
                  </div>
                  {errors.agreeToTerms && (
                    <p className="text-red-500 text-sm mt-1">{errors.agreeToTerms}</p>
                  )}
                </div>

                {/* Submit Button */}
                <button 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[#493628] text-white py-3 px-6 rounded-lg font-semibold hover:bg-[#AB886D] transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Memproses Booking...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="h-5 w-5 mr-2" />
                      Konfirmasi Booking
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingPage;