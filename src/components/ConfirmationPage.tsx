import React from 'react';
import { CheckCircle2, Download, ArrowLeft, Phone, Mail, Copy } from 'lucide-react';
import { dresses } from '../data/dresses';

interface ConfirmationPageProps {
  orderId: string;
  onNavigate: (page: string) => void;
}

const ConfirmationPage: React.FC<ConfirmationPageProps> = ({ orderId, onNavigate }) => {
  // Mock data - in real app, this would be fetched based on orderId
  const orderData = {
    id: orderId,
    dress: dresses[0], // Using first dress as example
    startDate: '2025-01-20',
    endDate: '2025-01-22',
    size: 'M',
    duration: 3,
    totalPrice: dresses[0].price * 3,
    deposit: dresses[0].deposit,
    customerInfo: {
      fullName: 'Siti Aminah',
      email: '<EMAIL>',
      phone: '08123456789',
      address: 'Jl. Malioboro No. 45, Yogyakarta'
    },
    orderDate: new Date().toISOString(),
    paymentDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
  };

  const totalPayment = orderData.totalPrice + orderData.deposit;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Nomor rekening telah disalin!');
  };

  return (
    <div className="min-h-screen bg-[#E4E0E1]">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle2 className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-[#493628] mb-2">
            Pemesanan Berhasil Dibuat!
          </h1>
          <p className="text-lg text-gray-600">
            Terima kasih atas kepercayaan Anda menggunakan layanan kami
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Info */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-[#493628] mb-4">Detail Pemesanan</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-gray-600">Nomor Pemesanan</p>
                  <p className="font-semibold text-[#493628] text-lg">{orderData.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Tanggal Pemesanan</p>
                  <p className="font-semibold text-[#493628]">
                    {new Date(orderData.orderDate).toLocaleDateString('id-ID', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>

              {/* Dress Info */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex gap-4">
                  <img 
                    src={orderData.dress.images[0]} 
                    alt={orderData.dress.name}
                    className="w-24 h-24 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-[#493628] mb-1">{orderData.dress.name}</h3>
                    <p className="text-sm text-gray-600 mb-1">{orderData.dress.category} - {orderData.dress.color}</p>
                    <p className="text-sm text-gray-600 mb-2">Ukuran: {orderData.size}</p>
                    <div className="text-sm">
                      <p>Tanggal Sewa: {new Date(orderData.startDate).toLocaleDateString('id-ID')} - {new Date(orderData.endDate).toLocaleDateString('id-ID')}</p>
                      <p>Durasi: {orderData.duration} hari</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Info */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-[#493628] mb-4">Informasi Penyewa</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Nama Lengkap</p>
                  <p className="font-semibold text-[#493628]">{orderData.customerInfo.fullName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-semibold text-[#493628]">{orderData.customerInfo.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Nomor Telepon</p>
                  <p className="font-semibold text-[#493628]">{orderData.customerInfo.phone}</p>
                </div>
                <div className="md:col-span-2">
                  <p className="text-sm text-gray-600">Alamat</p>
                  <p className="font-semibold text-[#493628]">{orderData.customerInfo.address}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Instructions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
              <h2 className="text-xl font-bold text-[#493628] mb-4">Instruksi Pembayaran</h2>
              
              {/* Payment Summary */}
              <div className="bg-[#E4E0E1] p-4 rounded-lg mb-4">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Harga Sewa:</span>
                    <span>Rp {orderData.totalPrice.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>DP (Uang Muka):</span>
                    <span>Rp {orderData.deposit.toLocaleString()}</span>
                  </div>
                  <div className="border-t border-gray-300 pt-2 flex justify-between font-bold text-[#493628]">
                    <span>Total Pembayaran:</span>
                    <span>Rp {totalPayment.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Bank Details */}
              <div className="border border-[#AB886D] rounded-lg p-4 mb-4">
                <h3 className="font-semibold text-[#493628] mb-3">Transfer ke:</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <p className="text-gray-600">Bank</p>
                    <p className="font-semibold">Bank Mandiri</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Nomor Rekening</p>
                    <div className="flex items-center justify-between">
                      <p className="font-semibold">**********</p>
                      <button 
                        onClick={() => copyToClipboard('**********')}
                        className="p-1 text-[#493628] hover:text-[#AB886D] transition-colors"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-600">Atas Nama</p>
                    <p className="font-semibold">GaunSyariJogja</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Jumlah Transfer</p>
                    <p className="font-bold text-[#493628] text-lg">Rp {totalPayment.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {/* Payment Deadline */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <p className="text-sm text-yellow-800">
                  <strong>Batas Waktu Pembayaran:</strong><br />
                  {new Date(orderData.paymentDeadline).toLocaleDateString('id-ID', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button className="w-full bg-[#493628] text-white py-2 px-4 rounded-lg hover:bg-[#AB886D] transition-colors flex items-center justify-center">
                  <Download className="h-4 w-4 mr-2" />
                  Download Invoice
                </button>
                <button 
                  onClick={() => onNavigate('account')}
                  className="w-full border border-[#493628] text-[#493628] py-2 px-4 rounded-lg hover:bg-[#E4E0E1] transition-colors"
                >
                  Lihat Riwayat Pemesanan
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-bold text-[#493628] mb-4">Langkah Selanjutnya</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-[#493628] font-bold">1</span>
              </div>
              <h3 className="font-semibold text-[#493628] mb-2">Transfer Pembayaran</h3>
              <p className="text-sm text-gray-600">Lakukan transfer sesuai nominal dan rekening yang tertera</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-[#493628] font-bold">2</span>
              </div>
              <h3 className="font-semibold text-[#493628] mb-2">Konfirmasi Pembayaran</h3>
              <p className="text-sm text-gray-600">Hubungi kami dengan bukti transfer untuk konfirmasi</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#D6C0B3] rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-[#493628] font-bold">3</span>
              </div>
              <h3 className="font-semibold text-[#493628] mb-2">Siap Diambil</h3>
              <p className="text-sm text-gray-600">Gaun akan siap untuk diambil atau dikirim</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-8 bg-[#493628] text-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-bold mb-4">Butuh Bantuan?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <Phone className="h-5 w-5 mr-3 text-[#D6C0B3]" />
              <div>
                <p className="font-semibold">WhatsApp</p>
                <p className="text-[#D6C0B3]">+62 812-3456-7890</p>
              </div>
            </div>
            <div className="flex items-center">
              <Mail className="h-5 w-5 mr-3 text-[#D6C0B3]" />
              <div>
                <p className="font-semibold">Email</p>
                <p className="text-[#D6C0B3]"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <button 
            onClick={() => onNavigate('home')}
            className="flex items-center justify-center bg-[#AB886D] text-white px-6 py-3 rounded-lg hover:bg-[#493628] transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Kembali ke Beranda
          </button>
          <button 
            onClick={() => onNavigate('catalog')}
            className="bg-[#D6C0B3] text-[#493628] px-6 py-3 rounded-lg hover:bg-[#AB886D] hover:text-white transition-colors"
          >
            Lihat Koleksi Lainnya
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationPage;